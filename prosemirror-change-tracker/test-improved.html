<!DOCTYPE html>
<html>
<head>
    <title>Improved ProseMirror Change Tracker Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .container {
            display: flex;
            gap: 20px;
        }
        
        .editor-section {
            flex: 2;
        }
        
        .controls-section {
            flex: 1;
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
        }
        
        #editor {
            border: 1px solid #ccc;
            padding: 10px;
            min-height: 200px;
            margin-bottom: 10px;
        }
        
        .commit {
            margin-bottom: 8px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            cursor: pointer;
        }
        
        .commit:hover {
            background: #fff3cd;
        }
        
        .commit-time {
            background: #007bff;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            margin-right: 8px;
        }
        
        .commit-revert {
            background: #dc3545;
            color: white;
            border: none;
            padding: 2px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            margin-left: 8px;
        }
        
        .blame-marker {
            background: #fff3cd;
        }
        
        .addition-highlight {
            background: #d4edda !important;
            border-radius: 2px;
            padding: 0 2px;
        }
        
        .deletion-highlight {
            background: #f8d7da !important;
            text-decoration: line-through !important;
            border-radius: 2px;
            padding: 0 2px;
            margin: 0 1px;
            display: inline-block;
        }
        
        .commit-form {
            margin-bottom: 20px;
        }
        
        .commit-form input {
            width: 200px;
            padding: 5px;
            margin-right: 10px;
        }
        
        .commit-form button {
            padding: 5px 15px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        
        .commit-form button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .instructions {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .instructions h3 {
            margin-top: 0;
        }
        
        .instructions ol {
            margin-bottom: 0;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        
        .status.has-changes {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
        }
        
        .status.no-changes {
            background: #d4edda;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <h1>Improved ProseMirror Change Tracker</h1>
    
    <div class="instructions">
        <h3>Test Instructions:</h3>
        <ol>
            <li>Type some text in the editor</li>
            <li>Click "Commit Changes" to save your changes</li>
            <li>Make more changes (add, delete, modify text)</li>
            <li>Commit again with a descriptive message</li>
            <li>Hover over commits to see what changed</li>
            <li>Notice how deletions are properly tracked and displayed</li>
        </ol>
    </div>
    
    <div class="container">
        <div class="editor-section">
            <div id="editor"></div>
            
            <form class="commit-form" id="commit">
                <input type="text" id="message" name="message" placeholder="Commit message..." />
                <button id="commitbutton" type="submit">Commit Changes</button>
            </form>
        </div>
        
        <div class="controls-section">
            <div id="status" class="status no-changes">No uncommitted changes</div>
            
            <h3>Commit History</h3>
            <div id="commits"></div>
        </div>
    </div>

    <script type="module" src="./index.js"></script>
</body>
</html>
