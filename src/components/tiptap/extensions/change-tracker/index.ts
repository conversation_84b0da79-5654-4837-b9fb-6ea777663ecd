import { <PERSON>lug<PERSON>, Plugin<PERSON><PERSON> } from "@tiptap/pm/state";
import { Decoration, DecorationSet } from "@tiptap/pm/view";
import { Extension, Mark, mergeAttributes } from "@tiptap/core";
import { Step, StepMap, Transform } from "@tiptap/pm/transform";
import { Fragment } from "@tiptap/pm/model";

// Type definitions for step properties
interface ReplaceStep extends Step {
  jsonID: string;
  from: number;
  to: number;
  slice: {
    content: Fragment;
  };
}

// Constants
const MARK_INSERTION = "insertion";
const MARK_DELETION = "deletion";
const EXTENSION_NAME = "trackchange";

// Data structures for tracking changes
class Span {
  constructor(
    public from: number,
    public to: number,
    public commit: number | null,
    public type: "addition" | "deletion" = "addition"
  ) {}
}

class Commit {
  constructor(
    public message: string,
    public time: Date,
    public steps: Step[],
    public maps: StepMap[],
    public hidden?: boolean,
    public deletions: Array<{
      pos: number;
      content: string;
      length: number;
    }> = []
  ) {}
}

class TrackState {
  constructor(
    public blameMap: Span[],
    public commits: Commit[],
    public uncommittedSteps: Step[],
    public uncommittedMaps: StepMap[],
    public pendingDeletions: Array<{
      pos: number;
      content: string;
      length: number;
    }> = []
  ) {}

  // Apply a transform to this state
  applyTransform(transform: Transform): TrackState {
    // Invert the steps in the transaction, to be able to save them in the next commit
    const inverted = transform.steps.map((step, i) =>
      step.invert(transform.docs[i])
    );

    // Extract deletions from this transform
    const newDeletions = this.extractDeletions(transform);

    const newBlame = updateBlameMap(
      this.blameMap,
      transform,
      this.commits.length
    );

    // Create a new state—since these are part of the editor state, a
    // persistent data structure, they must not be mutated.
    return new TrackState(
      newBlame,
      this.commits,
      this.uncommittedSteps.concat(inverted),
      this.uncommittedMaps.concat(transform.mapping.maps),
      this.pendingDeletions.concat(newDeletions)
    );
  }

  // Extract deletion information from a transform
  extractDeletions(
    transform: Transform
  ): Array<{ pos: number; content: string; length: number }> {
    const deletions: Array<{ pos: number; content: string; length: number }> =
      [];
    let doc = transform.docs[0]; // Document before the transform

    for (let i = 0; i < transform.steps.length; i++) {
      const step = transform.steps[i] as ReplaceStep;
      if (step.jsonID === "replace" && step.from < step.to) {
        // This is a deletion or replacement
        const deletedContent = doc.textBetween(step.from, step.to);
        if (deletedContent) {
          deletions.push({
            pos: step.from,
            content: deletedContent,
            length: step.to - step.from,
          });
        }
      }
      // Update doc for next step
      if (i < transform.docs.length - 1) {
        doc = transform.docs[i + 1];
      }
    }

    return deletions;
  }

  // When a transaction is marked as a commit, this is used to put any
  // uncommitted steps into a new commit.
  applyCommit(message: string, time: Date): TrackState {
    if (this.uncommittedSteps.length == 0 && this.pendingDeletions.length == 0)
      return this;

    const commit = new Commit(
      message,
      time,
      this.uncommittedSteps,
      this.uncommittedMaps,
      false,
      this.pendingDeletions
    );

    return new TrackState(
      this.blameMap,
      this.commits.concat(commit),
      [],
      [],
      []
    );
  }
}

function updateBlameMap(map: Span[], transform: Transform, id: number): Span[] {
  const result: Span[] = [];
  const mapping = transform.mapping;
  for (let i = 0; i < map.length; i++) {
    const span = map[i];
    const from = mapping.map(span.from, 1),
      to = mapping.map(span.to, -1);
    if (from < to) result.push(new Span(from, to, span.commit));
  }

  for (let i = 0; i < mapping.maps.length; i++) {
    const map = mapping.maps[i],
      after = mapping.slice(i + 1);
    map.forEach((_s: number, _e: number, start: number, end: number) => {
      insertIntoBlameMap(result, after.map(start, 1), after.map(end, -1), id);
    });
  }

  return result;
}

function insertIntoBlameMap(
  map: Span[],
  from: number,
  to: number,
  commit: number
): void {
  if (from >= to) return;
  let pos = 0;
  let next: Span;
  for (; pos < map.length; pos++) {
    next = map[pos];
    if (next.commit == commit) {
      if (next.to >= from) break;
    } else if (next.to > from) {
      // Different commit, not before
      if (next.from < from) {
        // Sticks out to the left (loop below will handle right side)
        const left = new Span(next.from, from, next.commit);
        if (next.to > to) map.splice(pos++, 0, left);
        else map[pos++] = left;
      }
      break;
    }
  }

  while ((next = map[pos])) {
    if (next.commit == commit) {
      if (next.from > to) break;
      from = Math.min(from, next.from);
      to = Math.max(to, next.to);
      map.splice(pos, 1);
    } else {
      if (next.from >= to) break;
      if (next.to > to) {
        map[pos] = new Span(to, next.to, next.commit);
        break;
      } else {
        map.splice(pos, 1);
      }
    }
  }

  map.splice(pos, 0, new Span(from, to, commit));
}

// Plugin key for the track changes plugin
const trackPluginKey = new PluginKey("trackChanges");

// Main tracking plugin with proper batching
const trackPlugin = new Plugin({
  key: trackPluginKey,
  state: {
    init(_, instance) {
      return new TrackState(
        [new Span(0, instance.doc.content.size, null)],
        [],
        [],
        [],
        []
      );
    },
    apply(tr, tracked) {
      // Only track changes when explicitly committing via Ctrl+S or save button
      const commitMessage = tr.getMeta(trackPluginKey);
      if (commitMessage) {
        // Commit all accumulated changes at once
        tracked = tracked.applyCommit(commitMessage, new Date(tr.time));
      } else if (tr.docChanged) {
        // Accumulate changes but don't create individual steps for each keystroke
        // This batches all changes until a commit is made
        tracked = tracked.applyTransform(tr);
      }
      return tracked;
    },
  },
});

// Plugin for highlighting changes
const highlightPluginKey = new PluginKey("highlightChanges");

// Helper function to calculate diff between two commits
function calculateCommitDiff(
  trackState: TrackState,
  currentCommit: Commit
): Decoration[] {
  const commits = trackState.commits;
  const currentIndex = commits.indexOf(currentCommit);

  if (currentIndex === -1) return [];

  const decorations: Decoration[] = [];

  // Show only additions for the selected commit (content that belongs to this commit only)
  const currentSpans = trackState.blameMap.filter(
    (span) => span.commit === currentIndex
  );

  // Show additions (content that belongs to current commit)
  currentSpans.forEach((span) => {
    decorations.push(
      Decoration.inline(span.from, span.to, {
        class: "commit-diff-addition",
        "data-commit-message": currentCommit.message,
        "data-commit-time": currentCommit.time.toISOString(),
      })
    );
  });

  // Show deletions for this commit
  if (currentCommit.deletions && currentCommit.deletions.length > 0) {
    currentCommit.deletions.forEach((deletion) => {
      decorations.push(
        Decoration.widget(deletion.pos, () => {
          const span = document.createElement("span");
          span.className = "commit-diff-deletion";
          span.setAttribute("data-commit-message", currentCommit.message);
          span.setAttribute(
            "data-commit-time",
            currentCommit.time.toISOString()
          );
          span.textContent = deletion.content;
          span.style.backgroundColor = "#f8d7da";
          span.style.textDecoration = "line-through";
          span.style.borderRadius = "2px";
          span.style.padding = "0 2px";
          span.style.margin = "0 1px";
          span.style.display = "inline-block";
          span.title = `Deleted: "${deletion.content}"`;
          return span;
        })
      );
    });
  }

  return decorations;
}

const highlightPlugin = new Plugin({
  key: highlightPluginKey,
  state: {
    init() {
      return { deco: DecorationSet.empty, commit: null };
    },
    apply(tr, prev, oldState, state) {
      const highlight = tr.getMeta(highlightPluginKey);
      if (highlight && highlight.add != null && prev.commit != highlight.add) {
        const tState = trackPlugin.getState(oldState);
        const decos = calculateCommitDiff(tState, highlight.add);
        return {
          deco: DecorationSet.create(state.doc, decos),
          commit: highlight.add,
        };
      } else if (
        highlight &&
        (highlight.clear === true || highlight.clear != null)
      ) {
        return { deco: DecorationSet.empty, commit: null };
      } else if (tr.docChanged && prev.commit) {
        return { deco: prev.deco.map(tr.mapping, tr.doc), commit: prev.commit };
      } else {
        return prev;
      }
    },
  },
  props: {
    decorations(state) {
      return this.getState(state).deco;
    },
  },
});

// Insertion mark for tracking additions
export const InsertionMark = Mark.create({
  name: MARK_INSERTION,
  addAttributes() {
    return {
      "data-user-id": {
        type: "string",
        default: "",
      },
      "data-user-name": {
        type: "string",
        default: "",
      },
      "data-timestamp": {
        type: "string",
        default: "",
      },
    };
  },
  parseHTML() {
    return [{ tag: "ins" }];
  },
  renderHTML({ HTMLAttributes }) {
    return [
      "ins",
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
        style: "background-color: #d4edda; text-decoration: none;",
      }),
      0,
    ];
  },
});

// Deletion mark for tracking deletions
export const DeletionMark = Mark.create({
  name: MARK_DELETION,
  addAttributes() {
    return {
      "data-user-id": {
        type: "string",
        default: "",
      },
      "data-user-name": {
        type: "string",
        default: "",
      },
      "data-timestamp": {
        type: "string",
        default: "",
      },
    };
  },
  parseHTML() {
    return [{ tag: "del" }];
  },
  renderHTML({ HTMLAttributes }) {
    return [
      "del",
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
        style: "background-color: #f8d7da; text-decoration: line-through;",
      }),
      0,
    ];
  },
});

declare module "@tiptap/core" {
  interface Commands<ReturnType> {
    trackchange: {
      /**
       * Commit current changes with a message
       */
      commitChanges: (message: string) => ReturnType;
      /**
       * Check if there are uncommitted changes
       */
      hasUncommittedChanges: () => ReturnType;
      /**
       * Get the current track state
       */
      getTrackState: () => ReturnType;
      /**
       * Highlight a specific commit
       */
      highlightCommit: (commit: Commit | null) => ReturnType;
      /**
       * Clear all highlights
       */
      clearAllHighlights: () => ReturnType;
      /**
       * Restore latest content without highlights
       */
      restoreLatestContent: () => ReturnType;
      /**
       * Save document (alias for commitChanges)
       */
      saveDocument: (message?: string) => ReturnType;
    };
  }
}

// Main extension
export const TrackChangeExtension = Extension.create<{
  enabled: boolean;
  userId?: string;
  userName?: string;
}>({
  name: EXTENSION_NAME,

  addOptions() {
    return {
      enabled: true,
      userId: "",
      userName: "",
    };
  },

  addExtensions() {
    return [InsertionMark, DeletionMark];
  },

  addProseMirrorPlugins() {
    return [trackPlugin, highlightPlugin];
  },

  addCommands() {
    return {
      commitChanges:
        (message: string) =>
        ({ tr, dispatch }) => {
          if (dispatch) {
            tr.setMeta(trackPluginKey, message);
            dispatch(tr);
          }
          return true;
        },

      hasUncommittedChanges:
        () =>
        ({ state }) => {
          const trackState = trackPlugin.getState(state);
          return trackState?.uncommittedSteps.length > 0;
        },

      getTrackState:
        () =>
        ({ state, editor }) => {
          const trackState = trackPlugin.getState(state);
          // Store the track state in the editor's storage for external access
          if (editor && trackState) {
            editor.storage.trackchange.currentTrackState = trackState;
          }
          return true;
        },

      highlightCommit:
        (commit: Commit | null) =>
        ({ tr, dispatch }) => {
          if (dispatch) {
            if (commit) {
              tr.setMeta(highlightPluginKey, { add: commit });
            } else {
              tr.setMeta(highlightPluginKey, { clear: true });
            }
            dispatch(tr);
          }
          return true;
        },

      clearAllHighlights:
        () =>
        ({ tr, dispatch }) => {
          if (dispatch) {
            tr.setMeta(highlightPluginKey, { clear: true });
            dispatch(tr);
          }
          return true;
        },

      restoreLatestContent:
        () =>
        ({ tr, dispatch }) => {
          if (dispatch) {
            // Clear highlighting and force a clean state
            tr.setMeta(highlightPluginKey, { clear: true });
            dispatch(tr);
          }
          return true;
        },

      saveDocument:
        (message?: string) =>
        ({ tr, dispatch, state }) => {
          const trackState = trackPlugin.getState(state);
          if (trackState?.uncommittedSteps.length > 0) {
            if (dispatch) {
              tr.setMeta(trackPluginKey, message || "Document saved");
              dispatch(tr);
            }
            return true;
          }
          return false;
        },
    };
  },

  addStorage() {
    return {
      hasUncommittedChanges: false,
      currentTrackState: null,
    };
  },

  onTransaction({ editor }) {
    // Update storage to track if there are uncommitted changes
    const trackState = trackPlugin.getState(editor.state);
    this.storage.hasUncommittedChanges =
      trackState?.uncommittedSteps.length > 0;
  },
});

// Export types and classes for external use
export { Commit, TrackState, Span };

// Export utility functions
export * from "./utils";

export default TrackChangeExtension;
